#!/usr/bin/env python3
"""
验证MIP算法实现的正确性
"""
import numpy as np
import matplotlib.pyplot as plt
import imageio.v2 as iio
import SimpleITK as sitk

def load_and_analyze_dicom(dicom_dir):
    """加载DICOM数据并分析"""
    reader = sitk.ImageSeriesReader()
    series_ids = reader.GetGDCMSeriesIDs(dicom_dir)
    file_names = reader.GetGDCMSeriesFileNames(dicom_dir, series_ids[0])
    reader.SetFileNames(file_names)
    img = reader.Execute()
    
    vol_zyx = sitk.GetArrayFromImage(img).astype(np.float32)
    spacing = img.GetSpacing()
    
    print(f"原始体数据形状: {vol_zyx.shape} (z,y,x)")
    print(f"体素间距: {spacing} (x,y,z)")
    print(f"强度范围: [{vol_zyx.min():.1f}, {vol_zyx.max():.1f}]")
    
    return vol_zyx, spacing

def verify_mip_correctness(vol_zyx):
    """验证MIP算法的正确性"""
    print("\n=== MIP算法验证 ===")
    
    # 手动计算MIP
    mip_ap_manual = np.max(vol_zyx, axis=0)  # 沿z轴
    mip_lat_manual = np.max(vol_zyx, axis=2)  # 沿x轴  
    mip_si_manual = np.max(vol_zyx, axis=1)   # 沿y轴
    
    print(f"手动计算 MIP AP: {mip_ap_manual.shape}, 最大值: {mip_ap_manual.max():.3f}")
    print(f"手动计算 MIP LAT: {mip_lat_manual.shape}, 最大值: {mip_lat_manual.max():.3f}")
    print(f"手动计算 MIP SI: {mip_si_manual.shape}, 最大值: {mip_si_manual.max():.3f}")
    
    # 加载生成的MIP图像
    try:
        mip_ap_saved = iio.imread("tof_mip_AP.png").astype(np.float32) / 255.0
        mip_lat_saved = iio.imread("tof_mip_LAT.png").astype(np.float32) / 255.0
        mip_si_saved = iio.imread("tof_mip_SI.png").astype(np.float32) / 255.0
        
        print(f"保存的 MIP AP: {mip_ap_saved.shape}, 最大值: {mip_ap_saved.max():.3f}")
        print(f"保存的 MIP LAT: {mip_lat_saved.shape}, 最大值: {mip_lat_saved.max():.3f}")
        print(f"保存的 MIP SI: {mip_si_saved.shape}, 最大值: {mip_si_saved.max():.3f}")
        
        # 验证一致性（考虑预处理的影响）
        # 应用相同的预处理
        p1, p99 = np.percentile(vol_zyx, (1.0, 99.7))
        vol_normalized = np.clip(vol_zyx, p1, p99)
        vol_normalized = (vol_normalized - p1) / (p99 - p1 + 1e-6)
        
        mip_ap_norm = np.max(vol_normalized, axis=0)
        mip_lat_norm = np.max(vol_normalized, axis=2)
        mip_si_norm = np.max(vol_normalized, axis=1)
        
        # 比较差异
        diff_ap = np.abs(mip_ap_norm - mip_ap_saved).mean()
        diff_lat = np.abs(mip_lat_norm - mip_lat_saved).mean()
        diff_si = np.abs(mip_si_norm - mip_si_saved).mean()
        
        print(f"\n平均差异:")
        print(f"AP: {diff_ap:.6f}")
        print(f"LAT: {diff_lat:.6f}")
        print(f"SI: {diff_si:.6f}")
        
        if diff_ap < 0.001 and diff_lat < 0.001 and diff_si < 0.001:
            print("✅ MIP算法实现正确!")
        else:
            print("❌ MIP算法可能有问题")
            
    except FileNotFoundError as e:
        print(f"❌ 无法找到MIP图像文件: {e}")

def create_comparison_plot():
    """创建MIP结果的对比图"""
    try:
        mip_ap = iio.imread("tof_mip_AP.png")
        mip_lat = iio.imread("tof_mip_LAT.png") 
        mip_si = iio.imread("tof_mip_SI.png")
        
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        axes[0].imshow(mip_ap, cmap='gray')
        axes[0].set_title('MIP AP (前后位)')
        axes[0].axis('off')
        
        axes[1].imshow(mip_lat, cmap='gray')
        axes[1].set_title('MIP LAT (侧位)')
        axes[1].axis('off')
        
        axes[2].imshow(mip_si, cmap='gray')
        axes[2].set_title('MIP SI (上下位)')
        axes[2].axis('off')
        
        plt.tight_layout()
        plt.savefig('mip_comparison.png', dpi=150, bbox_inches='tight')
        print("✅ 保存对比图: mip_comparison.png")
        
    except Exception as e:
        print(f"❌ 创建对比图失败: {e}")

def analyze_tof_characteristics(vol_zyx):
    """分析TOF数据特征"""
    print("\n=== TOF数据特征分析 ===")
    
    # 计算强度直方图
    hist, bins = np.histogram(vol_zyx.flatten(), bins=100)
    
    # 找到高强度区域（血管）
    high_intensity_threshold = np.percentile(vol_zyx, 95)
    vessel_voxels = vol_zyx > high_intensity_threshold
    vessel_ratio = vessel_voxels.sum() / vol_zyx.size
    
    print(f"高强度阈值 (95%): {high_intensity_threshold:.3f}")
    print(f"血管体素比例: {vessel_ratio:.3%}")
    
    # 检查每个切片的血管分布
    vessel_per_slice = vessel_voxels.sum(axis=(1,2))  # 每个z切片的血管体素数
    max_vessel_slice = np.argmax(vessel_per_slice)
    
    print(f"血管最多的切片: {max_vessel_slice} (共{vessel_per_slice[max_vessel_slice]}个血管体素)")
    
    return high_intensity_threshold, vessel_ratio

if __name__ == "__main__":
    DICOM_DIR = "./tof3d_tra_uCS_701"
    
    # 加载和分析数据
    vol_zyx, spacing = load_and_analyze_dicom(DICOM_DIR)
    
    # 验证MIP正确性
    verify_mip_correctness(vol_zyx)
    
    # 分析TOF特征
    analyze_tof_characteristics(vol_zyx)
    
    # 创建对比图
    create_comparison_plot()
    
    print("\n=== 验证完成 ===")
