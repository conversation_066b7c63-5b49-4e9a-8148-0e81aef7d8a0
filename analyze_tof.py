#!/usr/bin/env python3
"""
分析TOF数据特征，找出血管增强的问题
"""
import numpy as np
import matplotlib.pyplot as plt
import SimpleITK as sitk
import imageio.v2 as iio

def analyze_tof_data(dicom_dir):
    """详细分析TOF数据"""
    reader = sitk.ImageSeriesReader()
    series_ids = reader.GetGDCMSeriesIDs(dicom_dir)
    file_names = reader.GetGDCMSeriesFileNames(dicom_dir, series_ids[0])
    reader.SetFileNames(file_names)
    img = reader.Execute()
    
    vol_zyx = sitk.GetArrayFromImage(img).astype(np.float32)
    spacing = img.GetSpacing()
    
    print(f"原始数据形状: {vol_zyx.shape}")
    print(f"原始强度范围: [{vol_zyx.min():.1f}, {vol_zyx.max():.1f}]")
    print(f"体素间距: {spacing}")
    
    # 分析强度分布
    plt.figure(figsize=(15, 10))
    
    # 1. 整体强度直方图
    plt.subplot(2, 3, 1)
    plt.hist(vol_zyx.flatten(), bins=100, alpha=0.7)
    plt.title('原始强度分布')
    plt.xlabel('强度值')
    plt.ylabel('频次')
    plt.yscale('log')
    
    # 2. 显示中间切片
    mid_slice = vol_zyx.shape[0] // 2
    plt.subplot(2, 3, 2)
    plt.imshow(vol_zyx[mid_slice], cmap='gray')
    plt.title(f'中间切片 (z={mid_slice})')
    plt.colorbar()
    
    # 3. 分析不同百分位数的阈值效果
    percentiles = [90, 95, 98, 99, 99.5, 99.9]
    thresholds = [np.percentile(vol_zyx, p) for p in percentiles]
    
    plt.subplot(2, 3, 3)
    plt.plot(percentiles, thresholds, 'o-')
    plt.title('百分位数阈值')
    plt.xlabel('百分位数')
    plt.ylabel('强度阈值')
    plt.grid(True)
    
    # 4. 测试不同阈值的血管提取效果
    for i, (p, thresh) in enumerate(zip([95, 98, 99.5], thresholds[1::2])):
        plt.subplot(2, 3, 4+i)
        vessel_mask = vol_zyx[mid_slice] > thresh
        plt.imshow(vessel_mask, cmap='gray')
        plt.title(f'阈值 {p}% = {thresh:.1f}')
    
    plt.tight_layout()
    plt.savefig('tof_analysis.png', dpi=150, bbox_inches='tight')
    print("保存分析图: tof_analysis.png")
    
    return vol_zyx, thresholds

def test_different_mip_approaches(vol_zyx):
    """测试不同的MIP方法"""
    print("\n=== 测试不同MIP方法 ===")
    
    # 方法1: 原始MIP (当前方法)
    p1, p99 = np.percentile(vol_zyx, (1.0, 99.7))
    vol_norm1 = np.clip(vol_zyx, p1, p99)
    vol_norm1 = (vol_norm1 - p1) / (p99 - p1 + 1e-6)
    mip1 = vol_norm1.max(axis=0)
    
    # 方法2: 更激进的阈值处理
    p5, p99_5 = np.percentile(vol_zyx, (5.0, 99.5))
    vol_norm2 = np.clip(vol_zyx, p5, p99_5)
    vol_norm2 = (vol_norm2 - p5) / (p99_5 - p5 + 1e-6)
    mip2 = vol_norm2.max(axis=0)
    
    # 方法3: 血管增强 - 只保留高强度区域
    vessel_threshold = np.percentile(vol_zyx, 98)  # 只保留前2%的高强度
    vol_vessels = np.where(vol_zyx > vessel_threshold, vol_zyx, 0)
    if vol_vessels.max() > 0:
        vol_vessels = vol_vessels / vol_vessels.max()
    mip3 = vol_vessels.max(axis=0)
    
    # 方法4: 对比度增强
    vol_contrast = vol_zyx.copy()
    # 应用gamma校正增强对比度
    gamma = 0.5  # 小于1会增强亮区域
    vol_contrast = vol_contrast / vol_contrast.max()
    vol_contrast = np.power(vol_contrast, gamma)
    mip4 = vol_contrast.max(axis=0)
    
    # 保存不同方法的结果
    methods = [
        (mip1, "method1_original"),
        (mip2, "method2_aggressive_clip"), 
        (mip3, "method3_vessel_only"),
        (mip4, "method4_gamma_enhanced")
    ]
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    axes = axes.flatten()
    
    for i, (mip, name) in enumerate(methods):
        # 保存图像
        iio.imwrite(f"mip_{name}.png", (mip * 255).astype(np.uint8))
        
        # 显示
        axes[i].imshow(mip, cmap='gray')
        axes[i].set_title(f'{name}\nMax: {mip.max():.3f}')
        axes[i].axis('off')
        
        print(f"{name}: 强度范围 [{mip.min():.3f}, {mip.max():.3f}]")
    
    plt.tight_layout()
    plt.savefig('mip_methods_comparison.png', dpi=150, bbox_inches='tight')
    print("保存MIP方法对比: mip_methods_comparison.png")
    
    return methods

def create_vessel_enhanced_mip(vol_zyx):
    """创建血管增强的MIP"""
    print("\n=== 创建血管增强MIP ===")
    
    # 1. 背景抑制 - 移除低强度区域
    background_threshold = np.percentile(vol_zyx, 50)  # 中位数作为背景阈值
    vol_bg_suppressed = np.where(vol_zyx > background_threshold, vol_zyx, 0)
    
    # 2. 血管增强 - 突出高强度区域
    vessel_threshold = np.percentile(vol_zyx, 95)  # 前5%作为血管
    vessel_mask = vol_zyx > vessel_threshold
    
    # 3. 创建增强版本
    vol_enhanced = vol_zyx.copy()
    
    # 对血管区域进行增强
    vol_enhanced[vessel_mask] = vol_enhanced[vessel_mask] * 2.0
    
    # 对背景进行抑制
    vol_enhanced[~vessel_mask] = vol_enhanced[~vessel_mask] * 0.3
    
    # 4. 归一化
    vol_enhanced = vol_enhanced / vol_enhanced.max()
    
    # 5. 生成MIP
    mip_enhanced = vol_enhanced.max(axis=0)
    
    # 保存结果
    iio.imwrite("mip_vessel_enhanced.png", (mip_enhanced * 255).astype(np.uint8))
    
    print(f"血管增强MIP: 强度范围 [{mip_enhanced.min():.3f}, {mip_enhanced.max():.3f}]")
    print(f"血管体素数量: {vessel_mask.sum()}")
    print(f"血管体素比例: {vessel_mask.sum() / vessel_mask.size:.3%}")
    
    return mip_enhanced, vessel_mask

if __name__ == "__main__":
    DICOM_DIR = "./tof3d_tra_uCS_701"
    
    # 分析TOF数据
    vol_zyx, thresholds = analyze_tof_data(DICOM_DIR)
    
    # 测试不同MIP方法
    methods = test_different_mip_approaches(vol_zyx)
    
    # 创建血管增强MIP
    mip_enhanced, vessel_mask = create_vessel_enhanced_mip(vol_zyx)
    
    print("\n=== 分析完成 ===")
    print("请查看生成的图像文件来比较不同方法的效果")
