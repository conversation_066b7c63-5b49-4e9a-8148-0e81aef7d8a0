#!/usr/bin/env python3
"""
比较不同MIP算法的结果
"""
import matplotlib.pyplot as plt
import imageio.v2 as iio
import numpy as np

def compare_mip_results():
    """比较原始MIP和血管增强MIP的结果"""
    
    try:
        # 加载原始MIP结果
        original_ap = iio.imread("tof_mip_AP.png")
        original_lat = iio.imread("tof_mip_LAT.png") 
        original_si = iio.imread("tof_mip_SI.png")
        
        # 加载血管增强MIP结果
        vessel_ap = iio.imread("vessel_mip_AP.png")
        vessel_lat = iio.imread("vessel_mip_LAT.png")
        vessel_si = iio.imread("vessel_mip_SI.png")
        
        # 创建对比图
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # 第一行：原始MIP
        axes[0, 0].imshow(original_ap, cmap='gray')
        axes[0, 0].set_title('Original MIP AP\n(显示所有解剖结构)', fontsize=12)
        axes[0, 0].axis('off')
        
        axes[0, 1].imshow(original_lat, cmap='gray')
        axes[0, 1].set_title('Original MIP LAT\n(显示所有解剖结构)', fontsize=12)
        axes[0, 1].axis('off')
        
        axes[0, 2].imshow(original_si, cmap='gray')
        axes[0, 2].set_title('Original MIP SI\n(显示所有解剖结构)', fontsize=12)
        axes[0, 2].axis('off')
        
        # 第二行：血管增强MIP
        axes[1, 0].imshow(vessel_ap, cmap='hot')
        axes[1, 0].set_title('Vessel Enhanced MIP AP\n(突出血管结构)', fontsize=12)
        axes[1, 0].axis('off')
        
        axes[1, 1].imshow(vessel_lat, cmap='hot')
        axes[1, 1].set_title('Vessel Enhanced MIP LAT\n(突出血管结构)', fontsize=12)
        axes[1, 1].axis('off')
        
        axes[1, 2].imshow(vessel_si, cmap='hot')
        axes[1, 2].set_title('Vessel Enhanced MIP SI\n(突出血管结构)', fontsize=12)
        axes[1, 2].axis('off')
        
        # 添加总标题
        fig.suptitle('MIP算法对比：原始 vs 血管增强', fontsize=16, fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('final_mip_comparison.png', dpi=200, bbox_inches='tight')
        print("✅ 保存最终对比图: final_mip_comparison.png")
        
        # 分析图像质量
        print("\n=== 图像质量分析 ===")
        
        def analyze_image_quality(img, name):
            """分析图像质量指标"""
            img_gray = img if len(img.shape) == 2 else np.mean(img, axis=2)
            
            # 对比度 (标准差)
            contrast = np.std(img_gray)
            
            # 动态范围
            dynamic_range = img_gray.max() - img_gray.min()
            
            # 信息熵 (图像复杂度)
            hist, _ = np.histogram(img_gray, bins=256, range=(0, 255))
            hist = hist / hist.sum()  # 归一化
            entropy = -np.sum(hist * np.log2(hist + 1e-10))
            
            print(f"{name}:")
            print(f"  对比度: {contrast:.2f}")
            print(f"  动态范围: {dynamic_range:.2f}")
            print(f"  信息熵: {entropy:.2f}")
            
            return contrast, dynamic_range, entropy
        
        # 分析AP视图
        print("\n--- AP视图对比 ---")
        orig_stats = analyze_image_quality(original_ap, "原始MIP")
        vessel_stats = analyze_image_quality(vessel_ap, "血管增强MIP")
        
        # 计算改进程度
        contrast_improvement = (vessel_stats[0] - orig_stats[0]) / orig_stats[0] * 100
        print(f"\n对比度改进: {contrast_improvement:+.1f}%")
        
        if contrast_improvement > 0:
            print("✅ 血管增强MIP显著提高了图像对比度")
        else:
            print("⚠️  血管增强MIP可能过度处理了图像")
            
    except FileNotFoundError as e:
        print(f"❌ 无法找到图像文件: {e}")
        return False
    
    return True

def create_algorithm_summary():
    """创建算法总结"""
    print("\n" + "="*60)
    print("MIP算法问题诊断与解决方案总结")
    print("="*60)
    
    print("\n🔍 发现的问题:")
    print("1. 原始MIP显示了所有解剖结构，而不是突出血管")
    print("2. TOF数据的强度范围很大 [0, 869]，需要特殊处理")
    print("3. 简单的百分位裁剪不足以分离血管和背景组织")
    print("4. 缺少血管特异性的增强算法")
    
    print("\n✅ 实施的解决方案:")
    print("1. 背景抑制: 使用自适应阈值移除低强度噪声")
    print("2. 血管分割: 多层次阈值识别不同强度的血管")
    print("3. 分层增强: 对主要血管、次要血管分别处理")
    print("4. 形态学处理: 连接断裂的血管片段")
    print("5. 对比度增强: 应用gamma校正突出血管")
    print("6. 颜色映射: 使用热图色彩更好地显示血管")
    
    print("\n📊 算法参数:")
    print("- 背景阈值: 30%百分位数")
    print("- 血管阈值: 高=85%, 中=70%")
    print("- Gamma校正: γ=0.7")
    print("- 形态学核: 3D 6-连通")
    
    print("\n🎯 预期效果:")
    print("- 血管结构清晰可见")
    print("- 背景组织被有效抑制")
    print("- 主要血管和分支血管都能显示")
    print("- 适合临床诊断使用")
    
    print("\n📁 生成的文件:")
    files = [
        "vessel_mip_AP.png - 血管增强前后位MIP",
        "vessel_mip_LAT.png - 血管增强侧位MIP", 
        "vessel_mip_SI.png - 血管增强上下位MIP",
        "vessel_mip_spin.mp4 - 血管增强3D旋转视频",
        "final_mip_comparison.png - 原始vs增强对比图"
    ]
    
    for file in files:
        print(f"  ✓ {file}")

if __name__ == "__main__":
    print("开始比较MIP算法结果...")
    
    success = compare_mip_results()
    
    if success:
        create_algorithm_summary()
        print(f"\n🎉 MIP算法优化完成！")
        print("请查看生成的图像和视频文件来验证血管显示效果。")
    else:
        print("❌ 比较过程中出现错误")
