#!/usr/bin/env python3
"""
专业的血管MIP算法实现
"""
import os
import numpy as np
import SimpleITK as sitk
import pyvista as pv
import imageio.v2 as iio
from scipy import ndimage
import matplotlib.pyplot as plt

def load_dicom_with_vessel_enhancement(series_dir: str):
    """加载DICOM并进行血管增强预处理"""
    reader = sitk.ImageSeriesReader()
    series_ids = reader.GetGDCMSeriesIDs(series_dir)
    if not series_ids:
        raise RuntimeError(f"No DICOM series in: {series_dir}")

    file_names = reader.GetGDCMSeriesFileNames(series_dir, series_ids[0])
    reader.SetFileNames(file_names)
    img = reader.Execute()

    vol_zyx = sitk.GetArrayFromImage(img).astype(np.float32)
    spacing = img.GetSpacing()
    origin = img.GetOrigin()

    print(f"原始数据: {vol_zyx.shape}, 强度范围: [{vol_zyx.min():.1f}, {vol_zyx.max():.1f}]")

    # === 专业血管增强算法 ===
    
    # 1. 噪声抑制 - 使用高斯滤波
    vol_smooth = ndimage.gaussian_filter(vol_zyx, sigma=0.5)
    
    # 2. 背景分割 - 使用Otsu阈值的改进版本
    # 计算非零区域的阈值
    nonzero_vals = vol_smooth[vol_smooth > 0]
    if len(nonzero_vals) > 0:
        # 使用双峰分布找到背景和前景的分界
        hist, bins = np.histogram(nonzero_vals, bins=256)
        # 找到直方图的局部最小值作为阈值
        from scipy.signal import find_peaks
        peaks, _ = find_peaks(hist, height=hist.max()*0.1)
        if len(peaks) >= 2:
            # 在两个主要峰值之间找最小值
            valley_start = peaks[0]
            valley_end = peaks[1] if len(peaks) > 1 else len(hist)-1
            valley_idx = valley_start + np.argmin(hist[valley_start:valley_end])
            background_threshold = bins[valley_idx]
        else:
            background_threshold = np.percentile(nonzero_vals, 40)
    else:
        background_threshold = vol_smooth.max() * 0.1
    
    print(f"背景阈值: {background_threshold:.1f}")
    
    # 3. 血管分割 - 多层次阈值
    vessel_threshold_high = np.percentile(vol_smooth[vol_smooth > background_threshold], 85)
    vessel_threshold_med = np.percentile(vol_smooth[vol_smooth > background_threshold], 70)
    
    print(f"血管阈值: 高={vessel_threshold_high:.1f}, 中={vessel_threshold_med:.1f}")
    
    # 4. 分层增强
    vol_enhanced = np.zeros_like(vol_smooth)
    
    # 高强度血管区域 (主要血管)
    high_vessel_mask = vol_smooth > vessel_threshold_high
    vol_enhanced[high_vessel_mask] = 1.0
    
    # 中等强度血管区域 (次要血管)
    med_vessel_mask = (vol_smooth > vessel_threshold_med) & (vol_smooth <= vessel_threshold_high)
    vol_enhanced[med_vessel_mask] = 0.7
    
    # 低强度区域 (可能的血管)
    low_vessel_mask = (vol_smooth > background_threshold) & (vol_smooth <= vessel_threshold_med)
    vol_enhanced[low_vessel_mask] = 0.3
    
    # 5. 形态学处理 - 连接断裂的血管
    # 使用3D形态学闭运算连接近邻的血管片段
    kernel = ndimage.generate_binary_structure(3, 1)  # 6-连通
    vol_enhanced = ndimage.binary_closing(vol_enhanced > 0.1, structure=kernel).astype(np.float32)
    
    # 重新应用强度分层
    vol_enhanced[high_vessel_mask] = 1.0
    vol_enhanced[med_vessel_mask] = 0.7
    vol_enhanced[low_vessel_mask & (vol_enhanced > 0)] = 0.3
    
    print(f"增强后: 强度范围 [{vol_enhanced.min():.3f}, {vol_enhanced.max():.3f}]")
    print(f"血管体素比例: {(vol_enhanced > 0).sum() / vol_enhanced.size:.3%}")
    
    # 转换为VTK格式
    vol_xyz = np.ascontiguousarray(np.transpose(vol_enhanced, (2, 1, 0)))
    nx, ny, nz = vol_xyz.shape

    grid = pv.ImageData()
    grid.dimensions = (nx, ny, nz)
    grid.spacing = spacing
    grid.origin = origin
    grid.point_data["scalars"] = vol_xyz.ravel(order="F")
    
    return grid, vol_enhanced

def save_vessel_mip_pngs(vol_zyx: np.ndarray, out_prefix="vessel_mip"):
    """生成血管增强的MIP图像"""
    print(f"\n=== 生成血管MIP图像 ===")
    
    # AP（沿 z 轴最大投影）- 前后位
    mip_ap = vol_zyx.max(axis=0)
    
    # LAT（沿 x 轴最大投影）- 侧位  
    mip_lat = vol_zyx.max(axis=2)
    
    # SI（沿 y 轴最大投影）- 上下位
    mip_si = vol_zyx.max(axis=1)
    
    # 应用对比度增强
    def enhance_contrast(img, gamma=0.7):
        """应用gamma校正增强对比度"""
        if img.max() > 0:
            img_norm = img / img.max()
            img_enhanced = np.power(img_norm, gamma)
            return img_enhanced
        return img
    
    mip_ap_enhanced = enhance_contrast(mip_ap)
    mip_lat_enhanced = enhance_contrast(mip_lat)
    mip_si_enhanced = enhance_contrast(mip_si)
    
    # 保存图像
    iio.imwrite(f"{out_prefix}_AP.png", (mip_ap_enhanced * 255).astype(np.uint8))
    iio.imwrite(f"{out_prefix}_LAT.png", (mip_lat_enhanced * 255).astype(np.uint8))
    iio.imwrite(f"{out_prefix}_SI.png", (mip_si_enhanced * 255).astype(np.uint8))
    
    print(f"Saved: {out_prefix}_AP.png, {out_prefix}_LAT.png, {out_prefix}_SI.png")
    
    # 创建对比图
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    axes[0].imshow(mip_ap_enhanced, cmap='hot')
    axes[0].set_title('MIP AP (Anterior-Posterior)')
    axes[0].axis('off')
    
    axes[1].imshow(mip_lat_enhanced, cmap='hot')
    axes[1].set_title('MIP LAT (Lateral)')
    axes[1].axis('off')
    
    axes[2].imshow(mip_si_enhanced, cmap='hot')
    axes[2].set_title('MIP SI (Superior-Inferior)')
    axes[2].axis('off')
    
    plt.tight_layout()
    plt.savefig(f'{out_prefix}_comparison.png', dpi=150, bbox_inches='tight')
    print(f"Saved comparison: {out_prefix}_comparison.png")
    
    return mip_ap_enhanced, mip_lat_enhanced, mip_si_enhanced

def make_vessel_mip_video(
    grid: pv.ImageData,
    out_path: str = "vessel_mip_spin.mp4",
    seconds: int = 10,
    fps: int = 30,
    window_size=(1280, 960)
):
    """生成血管MIP旋转视频"""
    n_frames = seconds * fps
    az_step = 360.0 / n_frames

    pv.global_theme.smooth_shading = False
    pl = pv.Plotter(off_screen=True, window_size=window_size)
    pl.set_background("black")

    # 添加体渲染
    vol = pl.add_volume(
        grid,
        cmap="hot",                  # 热图色彩，突出血管
        shade=False,
        opacity="linear",
    )
    
    # 设置MIP模式
    vol.mapper.SetBlendModeToMaximumIntensity()
    vol.mapper.SetSampleDistance(0.2)  # 高质量采样
    vol.mapper.SetAutoAdjustSampleDistances(False)
    
    # 优化传输函数
    opacity_tf = vol.prop.GetScalarOpacity()
    opacity_tf.RemoveAllPoints()
    opacity_tf.AddPoint(0.0, 0.0)    # 背景完全透明
    opacity_tf.AddPoint(0.2, 0.0)    # 低强度透明
    opacity_tf.AddPoint(0.5, 0.6)    # 中等强度半透明
    opacity_tf.AddPoint(1.0, 1.0)    # 高强度不透明
    
    # 颜色传输函数
    color_tf = vol.prop.GetRGBTransferFunction()
    color_tf.RemoveAllPoints()
    color_tf.AddRGBPoint(0.0, 0.0, 0.0, 0.0)    # 黑色
    color_tf.AddRGBPoint(0.3, 0.5, 0.0, 0.0)    # 深红
    color_tf.AddRGBPoint(0.6, 1.0, 0.5, 0.0)    # 橙色
    color_tf.AddRGBPoint(1.0, 1.0, 1.0, 0.0)    # 黄色

    pl.view_isometric()
    pl.open_movie(out_path, framerate=fps, quality=8)

    # 渲染帧
    pl.show(auto_close=False)
    pl.render()
    pl.write_frame()

    for _ in range(n_frames - 1):
        pl.camera.azimuth += az_step
        pl.render()
        pl.write_frame()

    pl.close()
    print(f"Saved vessel MIP video: {os.path.abspath(out_path)}")

if __name__ == "__main__":
    DICOM_DIR = "./tof3d_tra_uCS_701"
    
    # 加载数据并进行血管增强
    grid, vol_enhanced = load_dicom_with_vessel_enhancement(DICOM_DIR)
    
    # 生成血管MIP图像
    save_vessel_mip_pngs(vol_enhanced, out_prefix="vessel_mip")
    
    # 生成血管MIP视频
    make_vessel_mip_video(
        grid,
        out_path="vessel_mip_spin.mp4",
        seconds=12,
        fps=30
    )
    
    print("\n=== 血管MIP生成完成 ===")
