import os
import numpy as np
import SimpleITK as sitk
import pyvista as pv
import imageio.v2 as iio


# ======================
# 常用工具
# ======================
def robust_window(vol, pmin=50, pmax=99.8, gamma=None):
    """用百分位做窗宽/窗位，避免极亮离群值把动态范围压扁；可选轻微 gamma。"""
    lo, hi = np.percentile(vol, [pmin, pmax])
    vol = np.clip(vol, lo, hi)
    rng = max(hi - lo, 1e-6)
    vol = (vol - lo) / rng
    if gamma is not None:
        vol = np.power(vol, gamma)
    return vol


def resample_isotropic(itk_img, interp=sitk.sitkLinear):
    """将原始各向异性体素重采样为等方，提高 MIP 清晰度与一致性。"""
    sp = itk_img.GetSpacing()          # (sx, sy, sz)
    new_sp = [min(sp)] * 3
    size = itk_img.GetSize()           # (nx, ny, nz) in ITK (x,y,z)
    new_sz = [int(round(sz * s / new_sp[i])) for i, (sz, s) in enumerate(zip(size, sp))]

    rs = sitk.ResampleImageFilter()
    rs.SetInterpolator(interp)
    rs.SetOutputSpacing(new_sp)
    rs.SetSize(new_sz)
    rs.SetOutputDirection(itk_img.GetDirection())
    rs.SetOutputOrigin(itk_img.GetOrigin())
    return rs.Execute(itk_img)


# ======================
# 读取 DICOM → 构建体数据
# ======================
def load_dicom_series(series_dir: str):
    reader = sitk.ImageSeriesReader()
    series_ids = reader.GetGDCMSeriesIDs(series_dir)
    if not series_ids:
        raise RuntimeError(f"No DICOM series in: {series_dir}")

    file_names = reader.GetGDCMSeriesFileNames(series_dir, series_ids[0])
    reader.SetFileNames(file_names)
    img = reader.Execute()  # SimpleITK Image

    # 1) 等方重采样（大幅减少台阶/重影）
    img_iso = resample_isotropic(img)

    # 2) 方法4: 对比度增强（完全参照analyze_tof.py）
    vol_zyx = sitk.GetArrayFromImage(img_iso).astype(np.float32)  # (z,y,x)
    print(f"原始强度范围: [{vol_zyx.min():.1f}, {vol_zyx.max():.1f}]")

    # 应用gamma校正增强对比度（与analyze_tof.py方法4完全一致）
    vol_contrast = vol_zyx.copy()
    vol_contrast = vol_contrast / vol_contrast.max()  # 归一化到[0,1]
    gamma = 0.5  # 小于1会增强亮区域
    vol_zyx = np.power(vol_contrast, gamma)

    print(f"Gamma校正后强度范围: [{vol_zyx.min():.3f}, {vol_zyx.max():.3f}]")

    spacing = img_iso.GetSpacing()   # (sx, sy, sz) 物理单位
    origin = img_iso.GetOrigin()

    # VTK 使用 (x,y,z) & Fortran 顺序
    vol_xyz = np.ascontiguousarray(np.transpose(vol_zyx, (2, 1, 0)))  # (x,y,z)
    nx, ny, nz = vol_xyz.shape

    grid = pv.ImageData()
    grid.dimensions = (nx, ny, nz)
    grid.spacing = spacing
    grid.origin = origin
    grid.point_data["scalars"] = vol_xyz.ravel(order="F").astype(np.float32)

    print(f"Spacing (iso): {spacing}, volume shape (z,y,x): {vol_zyx.shape}")
    return grid, vol_zyx  # grid 用于 GPU 渲染，vol_zyx 用于快速 MIP PNG


# ======================
# 静态 MIP PNG 导出（CPU 快速）
# ======================
def save_mip_pngs(vol_zyx: np.ndarray, out_prefix="tof_mip"):
    # AP（沿 z 轴最大投影）- 前后位
    mip_ap = vol_zyx.max(axis=0)  # (y,x)
    iio.imwrite(f"{out_prefix}_AP.png", np.clip(mip_ap * 255, 0, 255).astype(np.uint8))

    # LAT（沿 x 轴最大投影）- 侧位
    mip_lat = vol_zyx.max(axis=2)  # (z,y)
    iio.imwrite(f"{out_prefix}_LAT.png", np.clip(mip_lat * 255, 0, 255).astype(np.uint8))

    # SI（沿 y 轴最大投影）- 上下位
    mip_si = vol_zyx.max(axis=1)  # (z,x)
    iio.imwrite(f"{out_prefix}_SI.png", np.clip(mip_si * 255, 0, 255).astype(np.uint8))

    print(f"Saved: {out_prefix}_AP.png, {out_prefix}_LAT.png, {out_prefix}_SI.png")
    print(f"MIP AP shape: {mip_ap.shape}, range: [{mip_ap.min():.3f}, {mip_ap.max():.3f}]")
    print(f"MIP LAT shape: {mip_lat.shape}, range: [{mip_lat.min():.3f}, {mip_lat.max():.3f}]")
    print(f"MIP SI shape: {mip_si.shape}, range: [{mip_si.min():.3f}, {mip_si.max():.3f}]")


# ======================
# GPU MIP 旋转视频（PyVista/VTK）
# ======================
def make_mip_spin_video(
    grid: pv.ImageData,
    out_path: str = "tof3d_mip_spin.mp4",
    seconds: int = 10,
    fps: int = 30,
    window_size=(2560, 1920),          # 分辨率拉高（医学图更清）
    codec: str = "h264_videotoolbox",  # macOS 原生硬件编码器
    bitrate: str = "50M"               # 高码率避免抹细节
):
    n_frames = seconds * fps
    az_step = 360.0 / n_frames

    pv.global_theme.smooth_shading = False  # MIP 不需要光照
    pl = pv.Plotter(off_screen=True, window_size=window_size)
    pl.set_background("black")

    # 添加体并启用 MIP
    vol = pl.add_volume(
        grid,
        cmap="gray",
        shade=False,
        opacity="linear",
    )
    mapper = vol.mapper
    mapper.SetBlendModeToMaximumIntensity()

    # 简化透明度设置，参照analyze_tof.py的简洁方法
    opacity_tf = vol.prop.GetScalarOpacity()
    opacity_tf.RemoveAllPoints()
    opacity_tf.AddPoint(0.0, 0.0)    # 背景透明
    opacity_tf.AddPoint(0.3, 0.1)    # 低强度区域微弱显示
    opacity_tf.AddPoint(0.7, 0.8)    # 血管区域清晰显示
    opacity_tf.AddPoint(1.0, 1.0)    # 最亮区域完全显示

    # 关键：采样距离跟体素间距走 + 抖动抗走样 + 自动步长
    sd = 0.2 * min(grid.spacing)  # 更小的采样距离，提高血管细节
    mapper.SetSampleDistance(sd)
    mapper.SetAutoAdjustSampleDistances(False)  # 禁用自动调整，保持一致性
    try:
        mapper.SetUseJittering(True)
    except Exception:
        pass
    try:
        mapper.SetUseFloatingPointFrameBuffer(True)
    except Exception:
        pass

    pl.view_isometric()

    # 打开视频写入（高码率 + CRF；yuv420p 兼容性最好）
    pl.open_movie(
        out_path,
        framerate=fps,
        codec=codec,
        bitrate=bitrate,
        quality=None,  # 让码率/CRF生效
        ffmpeg_params=["-pix_fmt", "yuv420p"],  # h264_videotoolbox 兼容参数
    )

    # 渲一帧初始化
    pl.show(auto_close=False)
    pl.render()
    pl.write_frame()

    # 环绕一周
    for _ in range(n_frames - 1):
        pl.camera.azimuth += az_step
        pl.render()
        pl.write_frame()

    pl.close()
    print(f"Saved: {os.path.abspath(out_path)}")


# ======================
# 主函数
# ======================
if __name__ == "__main__":
    DICOM_DIR = "./tof3d_tra_uCS_701"  # ← 按需修改

    grid, vol_zyx = load_dicom_series(DICOM_DIR)
    save_mip_pngs(vol_zyx, out_prefix="tof_mip")

    make_mip_spin_video(
        grid,
        out_path="tof3d_mip_spin.mp4",
        seconds=12,
        fps=30,
        window_size=(2560, 1920),
        codec="h264_videotoolbox",  # macOS 原生硬件编码器
        bitrate="50M"
    )
